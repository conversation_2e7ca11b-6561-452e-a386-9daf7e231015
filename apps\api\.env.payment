# Payment Gateway Integration Environment Variables

# VNPAY Configuration
VNPAY_TMN_CODE=your_vnpay_tmn_code
VNPAY_HASH_SECRET=your_vnpay_hash_secret
VNPAY_URL=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
VNPAY_API_URL=https://sandbox.vnpayment.vn/merchant_webapi/api/transaction
VNPAY_RETURN_URL=http://localhost:3000/payment/vnpay/callback
VNPAY_NOTIFY_URL=http://localhost:3000/api/payment/vnpay/ipn

# ZaloPay Configuration
ZALOPAY_APP_ID=your_zalopay_app_id
ZALOPAY_KEY1=your_zalopay_key1
ZALOPAY_KEY2=your_zalopay_key2
ZALOPAY_ENDPOINT=https://sb-openapi.zalopay.vn/v2/create
ZALOPAY_CALLBACK_URL=http://localhost:3000/api/payment/zalopay/callback

# MoMo Configuration
MOMO_PARTNER_CODE=your_momo_partner_code
MOMO_ACCESS_KEY=your_momo_access_key
MOMO_SECRET_KEY=your_momo_secret_key
MOMO_ENDPOINT=https://test-payment.momo.vn/v2/gateway/api/create
MOMO_RETURN_URL=http://localhost:3000/payment/momo/callback
MOMO_NOTIFY_URL=http://localhost:3000/api/payment/momo/ipn

# OnePay Configuration
ONEPAY_MERCHANT_ID=your_onepay_merchant_id
ONEPAY_ACCESS_CODE=your_onepay_access_code
ONEPAY_HASH_SECRET=your_onepay_hash_secret
ONEPAY_PAYMENT_URL=https://mtf.onepay.vn/onecomm-pay/vpc.op
ONEPAY_QUERY_URL=https://mtf.onepay.vn/onecomm-pay/Vpcdps.op
ONEPAY_RETURN_URL=http://localhost:3000/payment/onepay/callback

# Payment Configuration
PAYMENT_TIMEOUT=15
PAYMENT_CURRENCY=VND
PAYMENT_LOCALE=vn
