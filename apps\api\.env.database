# Database Configuration
DB_TYPE=postgresql
DB_HOST=localhost
DB_PORT=5432
DB_NAME=gosafe_booking
DB_USERNAME=gosafe_user
DB_PASSWORD=gosafe_password
DATABASE_URL=postgresql://gosafe_user:gosafe_password@localhost:5432/gosafe_booking

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://localhost:6379/0

# Elasticsearch Configuration
ELASTICSEARCH_NODE=http://localhost:9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
ELASTICSEARCH_INDEX_PREFIX=gosafe

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-here
SESSION_STORE=redis

# Cache Configuration
CACHE_TTL=3600
SEARCH_CACHE_TTL=1800
USER_SESSION_TTL=86400
